<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use App\Models\CategoryPrice;
use App\Models\Item;
use App\Models\NewUserSubscription;
use App\Models\Store;
use App\Models\User;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class DashboardController extends Controller
{
    public function index()
    {
        $data = [
            'rolesCount' => Role::count(),
            'permissionsCount' => Permission::count(),
            'usersCount' => User::count(),
            'storesCount' => Store::count(),
            'itemsCount' => Item::count(),
            'storePricingsCount' => NewUserSubscription::count(),
            'categoryPricingsCount' => CategoryPrice::count(),
            'adsCount' => Ad::count(),
        ];

        return view('backoffice.dashboard', compact('data'));
    }
}
