<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Logout;
use Illuminate\Support\Facades\Auth;

class UpdateLastActivity
{
    /**
     * Handle the event.
     *
     * @param Logout $event
     * @return void
     */
    public function handle(Logout $event): void
    {
        $user = Auth::user();
        if ($user) {
            $user->last_activity = now();
            $user->save();
        }
    }
}
